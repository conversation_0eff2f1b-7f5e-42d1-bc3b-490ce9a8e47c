user www-data;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
	worker_connections 1024;
}

http {
         server {
            listen 80;
            listen 443 ssl;
            ssl_certificate /etc/ssl/example.com+5.pem;
            ssl_certificate_key /etc/ssl/example.com+5-key.pem;
            server_name localhost 127.0.0.1;
            
            location /api {
                return 302 /api/;
            }

            location /api/ {
                proxy_pass http://api:8000/;

            }

            location /openapi.json {
                proxy_pass http://api:8000;

            }

            location /docs {
                proxy_pass http://api:8000;
            }

            location /redoc {
                proxy_pass http://api:8000;
            }

            location / {
                proxy_pass http://host.docker.internal:8080;
                proxy_set_header    X-Forwarded-For $remote_addr;
                proxy_set_header    X-Forwarded-Proto $remote_addr;
            }
        }
}