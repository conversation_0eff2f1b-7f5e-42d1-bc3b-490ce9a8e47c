{"name": "discovery-atlas", "version": "1.0.1", "private": true, "scripts": {"serve": "vite --open", "build": "vite build", "lint": "eslint .", "build-prod": "vite build --mode=production", "serve-prod": "vite --mode production", "deploy": "node scripts/gh-pages-deploy.js", "up": "taze major -I"}, "dependencies": {"@cznethub/cznet-vue-core": "^0.2.34", "@unhead/vue": "^1.11.20", "@vueuse/core": "^12.8.2", "@vueuse/head": "^2.0.0", "@vuex-orm/core": "^0.36.4", "deepmerge": "^4.3.1", "dompurify": "^3.2.4", "google-maps": "^4.3.3", "lodash.isequal": "^4.5.0", "pretty-bytes": "^6.1.1", "rxjs": "^7.8.2", "vite-plugin-vuetify": "^2.1.0", "vue": "^3.5.13", "vue-browser-detect-plugin": "^0.1.18", "vue-cookies": "^1.8.6", "vue-demi": "^0.14.10", "vue-facing-decorator": "^3.0.4", "vue-i18n": "^11.1.1", "vue-router": "^4.5.0", "vue-timeago3": "^2.3.2", "vuetify": "^3.7.15", "vuex": "^4.1.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@antfu/eslint-config": "^4.6.0", "@fortawesome/fontawesome-free": "^6.7.2", "@iconify-json/carbon": "^1.2.8", "@mdi/font": "^7.4.47", "@vitejs/plugin-vue": "^5.2.1", "@vue/test-utils": "^2.4.6", "cross-env": "^7.0.3", "cypress": "^14.1.0", "cypress-vite": "^1.6.0", "eslint": "^9.21.0", "eslint-plugin-cypress": "^4.1.0", "eslint-plugin-format": "^1.0.1", "lint-staged": "^15.4.3", "sass": "^1.85.1", "shiki": "^3.1.0", "taze": "^18.6.0", "typescript": "^5.8.2", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "unplugin-vue-macros": "^2.14.5", "vite": "^6.2.0", "vite-plugin-pwa": "^0.21.1", "vite-plugin-webfont-dl": "^3.10.4", "vite-ssg": "^25.2.0", "vite-ssg-sitemap": "^0.8.1", "vitest": "^3.0.8", "vue-tsc": "^2.2.8"}, "lint-staged": {"*": "eslint --fix"}}