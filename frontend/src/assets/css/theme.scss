// :root {

// }
@use '@fortawesome/fontawesome-free/css/all.min.css';
@use '@cznethub/cznet-vue-core/styles';

html {
  font-size: 16px;
}

body {
  font-size: 14px;
  font-size: 1.4rem;
}

section {
  padding: 4rem 0;
}

.gap-1 {
  gap: 1rem;
}

.gap-2 {
  gap: 2rem;
}

#main-container {
  height: 100%;

  & > .v-sheet {
    height: 100%;
    min-height: 50rem;
  }
}

.v-container {
  max-width: 1800px;
}

.banner {
  padding-top: 9rem;
  padding-bottom: 9rem;
}

.full-height {
  height: 100%;
}

.full-width {
  width: 100%;
}

.has-text-shadow {
  text-shadow: 1px 1px 2px rgba(0, 0 ,0 , 0.65);
}

.has-cursor-pointer {
  cursor: pointer;
}

.has-cursor-text {
  cursor: text;
}

// .has-background-dark--gradient {
//   background: linear-gradient(180deg, #0D0F15 0%, #1A1D27 100%);
// }

.is-clickable {
  cursor: pointer;
  transition: color 0.15s ease;
}

.is-flipped-x {
  transform: scaleX(-1);
}

.has-bg-white {
  background-color: #fff !important;
}

p {
  max-width: 70rem;
}

.is-borderless {
  border: 0 !important;
}

.v-messages {
  font-size: 1rem !important;
  font-weight: 400;
  line-height: 1.5rem;
  letter-spacing: 0.03125em !important;
  font-family: "Roboto", sans-serif !important;
  margin-top: 0;

  .v-messages__message {
    line-height: 1.5rem;
  }
}

mark {
  background-color: transparent;
  font-weight: bold;
  color: inherit;
}

p {
  max-width: 70rem;
}

.snip-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.snip-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}