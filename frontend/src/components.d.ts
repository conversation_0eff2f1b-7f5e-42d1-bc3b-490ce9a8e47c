/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AuthRedirect: typeof import('./components/account/auth-redirect.vue')['default']
    'Cd.footer': typeof import('./components/base/cd.footer.vue')['default']
    'Cd.rangeInput': typeof import('./components/search-results/cd.range-input.vue')['default']
    'Cd.search': typeof import('./components/search/cd.search.vue')['default']
    'Cd.searchMap': typeof import('./components/search-results/cd.search-map.vue')['default']
    'Cd.searchResults': typeof import('./components/search-results/cd.search-results.vue')['default']
    'Cd.spatialCoverageMap': typeof import('./components/search-results/cd.spatial-coverage-map.vue')['default']
    'Cz.login': typeof import('./components/account/cz.login.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
