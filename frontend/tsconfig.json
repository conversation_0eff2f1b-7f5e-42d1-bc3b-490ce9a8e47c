{
  "compilerOptions": {
    "target": "ESNext",
    "jsx": "preserve",
    "lib": ["DOM", "ESNext"],
    "useDefineForClassFields": false, // @see https://github.com/vuex-orm/vuex-orm/issues/734#issuecomment-850002524
    "experimentalDecorators": true,
    "baseUrl": ".",
    "module": "ESNext",
    "moduleResolution": "node",
    "paths": {
      "@/*": ["src/*"],
    },
    "resolveJsonModule": true,
    "types": [
      "vitest",
      "vite/client",
      "vite-plugin-pwa/client"
    ],
    "allowJs": true,
    "strict": true,
    "strictNullChecks": true,
    "noUnusedLocals": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "exclude": ["dist", "node_modules", "cypress"]
}
