#!/usr/bin/env python3
"""
Simple test to verify contentType parameter parsing
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8000"

def test_parameter_parsing():
    """Test different ways of passing contentType parameter"""
    
    print("🧪 Testing ContentType Parameter Parsing")
    print("=" * 50)
    
    test_cases = [
        {
            "name": "Single string parameter",
            "url": f"{API_BASE_URL}/api/discovery/search/debug?contentType=TimeSeriesAggregation",
            "expected_content_type": ["TimeSeriesAggregation"]
        },
        {
            "name": "Multiple parameters (repeated)",
            "url": f"{API_BASE_URL}/api/discovery/search/debug?contentType=TimeSeriesAggregation&contentType=Dataset",
            "expected_content_type": ["TimeSeriesAggregation", "Dataset"]
        },
        {
            "name": "Empty parameter",
            "url": f"{API_BASE_URL}/api/discovery/search/debug",
            "expected_content_type": []
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📊 Test {i}: {test_case['name']}")
        print(f"URL: {test_case['url']}")
        print("-" * 50)
        
        try:
            response = requests.get(test_case['url'])
            
            if response.status_code == 200:
                result = response.json()
                actual_content_type = result.get("query_params", {}).get("contentType", [])
                
                print(f"✅ Request successful")
                print(f"📋 Expected contentType: {test_case['expected_content_type']}")
                print(f"📋 Actual contentType: {actual_content_type}")
                
                if actual_content_type == test_case['expected_content_type']:
                    print("✅ Parameter parsing CORRECT")
                else:
                    print("❌ Parameter parsing INCORRECT")
                    
                # Show the must conditions to see if contentType filtering is applied
                must_conditions = result.get("must_conditions", [])
                content_type_conditions = [
                    cond for cond in must_conditions 
                    if 'additionalType' in str(cond)
                ]
                
                if content_type_conditions:
                    print(f"🎯 ContentType filtering applied: {content_type_conditions}")
                elif actual_content_type:
                    print("⚠️  ContentType provided but no filtering applied")
                else:
                    print("ℹ️  No contentType filtering (as expected)")
                    
            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Request failed with error: {e}")

if __name__ == "__main__":
    print("🚀 Starting Simple ContentType Parameter Tests")
    print("Make sure your API server is running at", API_BASE_URL)
    print()
    
    test_parameter_parsing()
    
    print("\n✅ Tests completed!")
    print("\nNext steps:")
    print("1. If parameter parsing is correct, test actual search results")
    print("2. If parameter parsing is incorrect, check FastAPI Query parameter handling")
