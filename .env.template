ARGO_HOST=https://workflows.argo.cuahsi.io
ARGO_NAMESPACE=workflows
ARGO_BEARER_TOKEN=Bearer v2:eyJhbGciOi... ARGO_TOKEN value from https://workflows.argo.cuahsi.io/userinfo

MONGO_URL=*******************************
MONGO_DATABASE=hydroshare

HYDROSHARE_MONGO_URL=empty
HYDROSHARE_MONGO_DATABASE=empty

OAUTH2_CLIENT_ID=discover
OAUTH2_CLIENT_SECRET=<Get From Keycloak>

MINIO_ACCESS_KEY=
MINIO_SECRET_KEY=
MINIO_API_URL=api.minio.cuahsi.io

VITE_APP_NAME=discover

VITE_APP_ORIGIN=http://localhost:8080

# for nested static deployment, set VITE_APP_BASE=/discover/
VITE_APP_BASE=/

# VITE_APP_FULL_URL=${VITE_APP_ORIGIN}${VITE_APP_BASE}
VITE_APP_FULL_URL=http://localhost:8080/

VITE_APP_API_HOST=localhost

# VITE_APP_API_URL=https://${VITE_APP_API_HOST}/api
VITE_APP_API_URL=https://localhost/api

# ALLOW_ORIGINS=${VITE_APP_ORIGIN}
ALLOW_ORIGINS=http://localhost:8080

# OAUTH2_REDIRECT_URL=${VITE_APP_API_URL}/auth/cuahsi/callback
OAUTH2_REDIRECT_URL=https://localhost/api/api/auth/cuahsi/callback

# VITE_OAUTH2_REDIRECT_URL="${VITE_APP_FULL_URL}#/auth-redirect"
VITE_OAUTH2_REDIRECT_URL="http://localhost:8080/#/auth-redirect"

OIDC_BASE_URL=https://auth.cuahsi.org/realms/CUAHSI/protocol/openid-connect/
