# Count Performance Optimizations

This document describes the performance optimizations implemented to address slow count operations in the discovery API.

## Problem

The original count implementation used MongoDB's `$count` aggregation stage, which requires processing all matching documents. This was causing significant performance issues, especially for large datasets and complex queries.

## Solutions Implemented

### 1. In-Memory Caching (Primary Optimization)

**What it does:** Caches count results to avoid repeated expensive operations.

**Benefits:**
- Near-instant response for repeated queries
- Configurable cache size (default: 1000 entries)
- Simple LRU eviction policy
- Significant performance improvement for common queries

**Usage:**
```python
# Use cache (default)
GET /search/count?use_cache=true

# Bypass cache
GET /search/count?use_cache=false
```

**API Response:**
```json
{
  "count": 1250
}
```

**Cache Management:**
```python
# Clear cache
POST /search/count/cache/clear

# Get cache statistics
GET /search/count/cache/stats
```

### 2. Combined Search + Count Endpoint

**What it does:** Returns both search results and count in a single request.

**Benefits:**
- Reduces round trips
- Better user experience
- Uses cached count when available

**Usage:**
```python
# Search with count included
GET /search?include_count=true&pageSize=30

# Search without count (faster)
GET /search?include_count=false&pageSize=30
```

### 3. Optimized Keyword Filtering

**What it does:** Uses exact `term` matching instead of `text` search for keyword filters.

**Benefits:**
- Faster filtering performance
- More precise matching
- Better for exact keyword filtering

**Implementation:**
```python
# Before (slower text search)
{'text': {'path': 'keywords', 'query': keyword}}

# After (faster exact matching)
{'term': {'path': 'keywords', 'query': keyword}}
```

## Performance Comparison

| Method | Speed | Accuracy | Use Case |
|--------|-------|----------|----------|
| Fresh Count (`$count`) | Slow (1-10s) | 100% | First-time queries |
| Cached Count | Very Fast (<0.01s) | 100% | Repeated queries |
| Combined Search+Count | Moderate (0.5-2s) | 100% | Initial page load |

## Configuration

### Environment Variables

```bash
# Search relevance threshold (affects count accuracy)
SEARCH_RELEVANCE_SCORE_THRESHOLD=1.0
```

### Cache Settings

```python
# In router.py
_cache_max_size = 1000  # Maximum cache entries
```

## Usage Recommendations

### For UI Applications

1. **Initial page load:** Use `GET /search?include_count=true` for single request
2. **Pagination:** Use cached counts for subsequent pages
3. **Filter changes:** Use cached counts for fast feedback
4. **Enable caching:** Always use `use_cache=true` for better performance

### For APIs

1. **Enable caching:** Use `use_cache=true` by default for better performance
2. **Cache management:** Clear cache when data is updated
3. **Monitor performance:** Use cache stats endpoint for monitoring
4. **Single requests:** Use combined search+count when possible

## Testing

Run the performance test script to compare different methods:

```bash
python test_count_performance.py
```

This will test:
- Fresh vs cached counting
- Cache performance improvements
- Combined search+count
- Cache management

## Monitoring

### Cache Statistics

```python
GET /search/count/cache/stats
```

Returns:
```json
{
  "cache_size": 45,
  "max_cache_size": 1000,
  "cache_keys": ["abc123...", "def456..."]
}
```

### Performance Metrics

Monitor these metrics in production:
- Average count response time
- Cache hit rate
- Memory usage of cache
- Query patterns and frequency

## Troubleshooting

### Cache Memory Issues

If cache uses too much memory:
1. Reduce `_cache_max_size`
2. Implement TTL-based expiration
3. Use external cache (Redis) for production

### Slow Performance Despite Optimizations

1. Check MongoDB Atlas Search index health
2. Verify query complexity
3. Consider query optimization
4. Monitor database performance metrics

## Future Improvements

1. **Redis Integration:** Replace in-memory cache with Redis for distributed systems
2. **TTL Caching:** Add time-based cache expiration
3. **Background Refresh:** Pre-compute counts for common queries
4. **Metrics Collection:** Add detailed performance monitoring
5. **Query Optimization:** Further optimize MongoDB aggregation pipelines
