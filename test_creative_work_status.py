#!/usr/bin/env python3
"""
Test script to verify creativeWorkStatus filtering with multiple values
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8000"

def test_creative_work_status_filtering():
    """Test creativeWorkStatus filtering with single and multiple values"""
    
    print("🧪 Testing CreativeWorkStatus Filtering")
    print("=" * 50)
    
    test_cases = [
        {
            "name": "Single creativeWorkStatus (Published)",
            "params": {"creativeWorkStatus": "Published"},
            "description": "Should only return records with creativeWorkStatus=Published"
        },
        {
            "name": "Single creativeWorkStatus (Draft)",
            "params": {"creativeWorkStatus": "Draft"},
            "description": "Should only return records with creativeWorkStatus=Draft"
        },
        {
            "name": "Multiple creativeWorkStatus",
            "params": {"creativeWorkStatus": ["Published", "Draft"]},
            "description": "Should return records with either Published or Draft status"
        },
        {
            "name": "No creativeWorkStatus filter",
            "params": {},
            "description": "Should return all records regardless of status"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📊 Test {i}: {test_case['name']}")
        print(f"Description: {test_case['description']}")
        print("-" * 50)
        
        # Test the debug endpoint first
        try:
            debug_params = {**test_case["params"], "pageSize": 5}
            debug_response = requests.get(
                f"{API_BASE_URL}/api/discovery/search/debug",
                params=debug_params
            )
            
            if debug_response.status_code == 200:
                debug_result = debug_response.json()
                creative_work_status = debug_result['query_params']['creativeWorkStatus']
                
                print(f"✅ Debug request successful")
                print(f"📋 CreativeWorkStatus parsed as: {creative_work_status}")
                
                # Look for creativeWorkStatus conditions in must
                must_conditions = debug_result['must_conditions']
                status_conditions = [
                    cond for cond in must_conditions 
                    if 'creativeWorkStatus' in str(cond)
                ]
                
                if status_conditions:
                    print(f"🎯 CreativeWorkStatus conditions: {len(status_conditions)} found")
                    for j, condition in enumerate(status_conditions):
                        print(f"   Condition {j+1}: {condition}")
                elif creative_work_status:
                    print("⚠️  CreativeWorkStatus provided but no filtering applied")
                else:
                    print("ℹ️  No creativeWorkStatus filtering (as expected)")
                    
            else:
                print(f"❌ Debug request failed with status {debug_response.status_code}")
                
        except Exception as e:
            print(f"❌ Debug request failed: {e}")
        
        # Test the actual search
        try:
            search_params = {**test_case["params"], "pageSize": 5}
            search_response = requests.get(
                f"{API_BASE_URL}/api/discovery/search",
                params=search_params
            )
            
            if search_response.status_code == 200:
                result = search_response.json()
                records = result if isinstance(result, list) else result.get("results", [])
                
                print(f"🔍 Search successful - Found {len(records)} records")
                
                if records:
                    # Analyze the creative work status values in results
                    status_values = set()
                    for record in records:
                        status = record.get("creativeWorkStatus")
                        if isinstance(status, dict):
                            status_values.add(status.get("name", "Unknown"))
                        elif isinstance(status, str):
                            status_values.add(status)
                        else:
                            status_values.add("No Status")
                    
                    print(f"📈 Status values found: {sorted(status_values)}")
                    
                    # Check if results match expected filter
                    if "creativeWorkStatus" in test_case["params"]:
                        expected_statuses = test_case["params"]["creativeWorkStatus"]
                        if isinstance(expected_statuses, str):
                            expected_statuses = [expected_statuses]
                        
                        unexpected_statuses = status_values - set(expected_statuses) - {"No Status"}
                        if unexpected_statuses:
                            print(f"⚠️  Found unexpected statuses: {unexpected_statuses}")
                        else:
                            print("✅ All results match expected status filter")
                else:
                    print("ℹ️  No records returned")
                    
            else:
                print(f"❌ Search failed with status {search_response.status_code}")
                
        except Exception as e:
            print(f"❌ Search failed: {e}")

def test_parameter_formats():
    """Test different ways of passing creativeWorkStatus parameters"""
    
    print("\n🔧 Testing Parameter Formats")
    print("=" * 35)
    
    test_urls = [
        {
            "name": "Single parameter",
            "url": f"{API_BASE_URL}/api/discovery/search/debug?creativeWorkStatus=Published",
            "expected": ["Published"]
        },
        {
            "name": "Multiple parameters (repeated)",
            "url": f"{API_BASE_URL}/api/discovery/search/debug?creativeWorkStatus=Published&creativeWorkStatus=Draft",
            "expected": ["Published", "Draft"]
        },
        {
            "name": "Combined with contentType",
            "url": f"{API_BASE_URL}/api/discovery/search/debug?contentType=TimeSeriesAggregation&creativeWorkStatus=Published",
            "expected_status": ["Published"],
            "expected_content": ["TimeSeriesAggregation"]
        }
    ]
    
    for test in test_urls:
        print(f"\n🔎 {test['name']}")
        print(f"URL: {test['url']}")
        
        try:
            response = requests.get(test['url'])
            if response.status_code == 200:
                result = response.json()
                actual_status = result['query_params']['creativeWorkStatus']
                actual_content = result['query_params']['contentType']
                
                print(f"✅ Request successful")
                print(f"📋 CreativeWorkStatus: {actual_status}")
                print(f"📋 ContentType: {actual_content}")
                
                # Check expectations
                if 'expected' in test and actual_status == test['expected']:
                    print("✅ CreativeWorkStatus parsing correct")
                elif 'expected_status' in test:
                    if actual_status == test['expected_status'] and actual_content == test['expected_content']:
                        print("✅ Both parameters parsing correct")
                    else:
                        print("❌ Parameter parsing incorrect")
                        
            else:
                print(f"❌ Request failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    print("🚀 Starting CreativeWorkStatus Filtering Tests")
    print("Make sure your API server is running at", API_BASE_URL)
    print()
    
    test_creative_work_status_filtering()
    test_parameter_formats()
    
    print("\n✅ Tests completed!")
    print("\n💡 Summary:")
    print("- CreativeWorkStatus now accepts multiple values like contentType")
    print("- Uses exact term matching for precise filtering")
    print("- Supports both single and multiple status filtering")
