version: '3'


volumes:
  mongo_data:
    driver: local

services:
  nginx:
    image: nginx:1.24.0
    volumes:
      - ./nginx/nginx-local-frontend-dev.conf:/etc/nginx/nginx.conf
      - ./nginx/config:/etc/ssl
    ports:
      - 443:443
    depends_on:
      - api
      # - frontend

  api:
    ports:
      - 8000:8000
      #- 5678:5678
    restart: unless-stopped
    volumes:
      - ./api/discover:/discover
    build:
      context: ./api/
      dockerfile: Dockerfile
    #command: ["pip install debugpy -t /tmp && python /tmp/debugpy --wait-for-client --listen 0.0.0.0:5678 -m uvicorn api.main:app --host 0.0.0.0 --port 8000"]
    env_file:
      - .env

  # frontend:
  #   image: iguide_frontend
  #   build:
  #     dockerfile: ./frontend/Dockerfile
  #   volumes:
  #     - ./frontend:/app
  #     - ./frontend/nginx.conf:/etc/nginx/nginx.conf
  #   ports:
  #     - 5001:5001
  #   restart: unless-stopped
  #   depends_on:
  #     - api

