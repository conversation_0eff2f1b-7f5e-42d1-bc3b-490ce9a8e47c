#!/usr/bin/env python3
"""
Test script to verify contentType filtering is working correctly.
This script tests the specific issue where contentType=TimeSeriesAggregation
was returning records that don't match the specified content type.
"""

import requests
import json
from typing import Dict, Any, List

# Configuration
API_BASE_URL = "http://localhost:8000"  # Adjust as needed

def test_content_type_filtering():
    """Test contentType filtering with specific values"""
    
    print("🧪 Testing ContentType Filtering")
    print("=" * 50)
    
    # Test cases for contentType filtering
    test_cases = [
        {
            "name": "TimeSeriesAggregation",
            "contentType": ["TimeSeriesAggregation"],
            "description": "Should only return records with exact contentType match"
        },
        {
            "name": "Dataset", 
            "contentType": ["Dataset"],
            "description": "Should only return records with contentType=Dataset"
        },
        {
            "name": "Multiple content types",
            "contentType": ["TimeSeriesAggregation", "Dataset"],
            "description": "Should return records matching either content type"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📊 Test {i}: {test_case['name']}")
        print(f"Description: {test_case['description']}")
        print("-" * 50)
        
        # Make the search request
        try:
            response = requests.get(
                f"{API_BASE_URL}/api/discovery/search",
                params={
                    "contentType": test_case["contentType"],
                    "pageSize": 10,
                    "pageNumber": 1
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                records = result if isinstance(result, list) else result.get("results", [])
                
                print(f"✅ Request successful - Found {len(records)} records")
                
                # Analyze the results
                content_types_found = set()
                mismatched_records = []
                
                for record in records:
                    record_content_type = record.get("additionalType", "")
                    content_types_found.add(record_content_type)
                    
                    # Check if this record matches the expected content types
                    if record_content_type not in test_case["contentType"]:
                        mismatched_records.append({
                            "name": record.get("name", "Unknown"),
                            "additionalType": record_content_type,
                            "id": record.get("_id", record.get("id", "Unknown"))
                        })
                
                print(f"📋 Content types found: {sorted(content_types_found)}")
                
                if mismatched_records:
                    print(f"❌ Found {len(mismatched_records)} records with incorrect content types:")
                    for record in mismatched_records[:5]:  # Show first 5 mismatches
                        print(f"   - {record['name']} (ID: {record['id']}) has contentType: '{record['additionalType']}'")
                    if len(mismatched_records) > 5:
                        print(f"   ... and {len(mismatched_records) - 5} more")
                else:
                    print("✅ All records have correct content types")
                    
            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Request failed with error: {e}")

def test_search_query_structure():
    """Test the actual MongoDB query structure being generated"""

    print("\n🔍 Testing Search Query Structure")
    print("=" * 40)

    test_queries = [
        {"contentType": ["TimeSeriesAggregation"]},
        {"contentType": ["Dataset"]},
        {"contentType": ["TimeSeriesAggregation", "Dataset"]},
        {"term": "water", "contentType": ["TimeSeriesAggregation"]},
    ]

    for query in test_queries:
        print(f"\n🔎 Testing query: {query}")
        try:
            # Test the debug endpoint to see the actual MongoDB query
            debug_response = requests.get(
                f"{API_BASE_URL}/api/discovery/search/debug",
                params=query
            )

            if debug_response.status_code == 200:
                debug_result = debug_response.json()
                print(f"✅ Debug query successful")
                print(f"📋 ContentType parsed as: {debug_result['query_params']['contentType']}")
                print(f"🔧 Must conditions: {len(debug_result['must_conditions'])}")

                # Look for contentType conditions in must
                content_type_conditions = [
                    cond for cond in debug_result['must_conditions']
                    if 'additionalType' in str(cond)
                ]
                if content_type_conditions:
                    print(f"🎯 ContentType conditions: {content_type_conditions}")
                else:
                    print("⚠️  No contentType conditions found in must clauses")
            else:
                print(f"❌ Debug query failed with status {debug_response.status_code}")

            # Also test the actual search
            response = requests.get(
                f"{API_BASE_URL}/api/discovery/search",
                params={**query, "pageSize": 1}
            )

            if response.status_code == 200:
                result = response.json()
                count = len(result) if isinstance(result, list) else len(result.get("results", []))
                print(f"✅ Search executed successfully - {count} records returned")
            else:
                print(f"❌ Search failed with status {response.status_code}")

        except Exception as e:
            print(f"❌ Query failed: {e}")

def analyze_sample_records():
    """Analyze a sample of records to understand the additionalType field structure"""
    
    print("\n📊 Analyzing Sample Records")
    print("=" * 35)
    
    try:
        # Get a sample of records without any contentType filter
        response = requests.get(
            f"{API_BASE_URL}/api/discovery/search",
            params={"pageSize": 20, "pageNumber": 1}
        )
        
        if response.status_code == 200:
            result = response.json()
            records = result if isinstance(result, list) else result.get("results", [])
            
            print(f"📋 Analyzing {len(records)} sample records...")
            
            # Analyze additionalType field structure
            additional_type_analysis = {}
            
            for record in records:
                additional_type = record.get("additionalType")
                type_info = {
                    "type": type(additional_type).__name__,
                    "value": additional_type
                }
                
                key = f"{type_info['type']}: {additional_type}"
                if key not in additional_type_analysis:
                    additional_type_analysis[key] = 0
                additional_type_analysis[key] += 1
            
            print("\n📈 AdditionalType field analysis:")
            for type_info, count in sorted(additional_type_analysis.items()):
                print(f"   {type_info} - {count} records")
                
        else:
            print(f"❌ Failed to get sample records: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Analysis failed: {e}")

if __name__ == "__main__":
    print("🚀 Starting ContentType Filtering Tests")
    print("Make sure your API server is running at", API_BASE_URL)
    print()
    
    # Run the tests
    analyze_sample_records()
    test_content_type_filtering()
    test_search_query_structure()
    
    print("\n✅ Tests completed!")
    print("\n💡 If you see mismatched records, the contentType filtering needs adjustment.")
    print("Check the MongoDB search index configuration for the 'additionalType' field.")
