#!/usr/bin/env python3
"""
Test script to compare count performance between different methods.
Run this script to test the performance improvements.
"""

import asyncio
import time
import requests
import json
from typing import Dict, Any

# Configuration
API_BASE_URL = "http://localhost:8000"  # Adjust as needed
TEST_QUERIES = [
    {"term": "water"},
    {"term": "climate"},
    {"keyword": "hydrology"},
    {"contentType": "Dataset"},
    {"creatorName": "Smith"},
    {"term": "water", "keyword": "hydrology"},
]

async def test_count_performance():
    """Test different count methods and compare performance"""
    
    print("🧪 Testing Count Performance Optimizations")
    print("=" * 50)
    
    results = []
    
    for i, query in enumerate(TEST_QUERIES, 1):
        print(f"\n📊 Test {i}: {query}")
        print("-" * 30)
        
        # Test 1: Fresh count (no cache)
        start_time = time.time()
        try:
            response = requests.get(
                f"{API_BASE_URL}/search/count",
                params={**query, "use_cache": False}
            )
            fresh_result = response.json()
            fresh_time = time.time() - start_time
            fresh_count = fresh_result.get("count", 0)
            print(f"✅ Fresh count: {fresh_count} (took {fresh_time:.2f}s)")
        except Exception as e:
            print(f"❌ Fresh count failed: {e}")
            fresh_time = None
            fresh_count = None

        # Test 2: Cached count (should be very fast on second call)
        start_time = time.time()
        try:
            response = requests.get(
                f"{API_BASE_URL}/search/count",
                params={**query, "use_cache": True}
            )
            cached_result = response.json()
            cached_time = time.time() - start_time
            cached_count = cached_result.get("count", 0)
            print(f"🚀 Cached count: {cached_count} (took {cached_time:.2f}s)")
        except Exception as e:
            print(f"❌ Cached count failed: {e}")
            cached_time = None
            cached_count = None

        # Test 3: Combined search + count
        start_time = time.time()
        try:
            response = requests.get(
                f"{API_BASE_URL}/search",
                params={**query, "include_count": True, "pageSize": 10}
            )
            combined_result = response.json()
            combined_time = time.time() - start_time
            combined_count = combined_result.get("meta", {}).get("count", {}).get("count", 0)
            docs_count = len(combined_result.get("docs", []))
            print(f"🔄 Combined search+count: {combined_count} total, {docs_count} docs (took {combined_time:.2f}s)")
        except Exception as e:
            print(f"❌ Combined search+count failed: {e}")
            combined_time = None
            combined_count = None
        
        # Calculate performance improvements
        if fresh_time and cached_time:
            cache_speedup = fresh_time / cached_time
            print(f"💾 Cache speedup: {cache_speedup:.1f}x faster")

        results.append({
            "query": query,
            "fresh_time": fresh_time,
            "fresh_count": fresh_count,
            "cached_time": cached_time,
            "cached_count": cached_count,
            "combined_time": combined_time,
            "combined_count": combined_count,
        })
    
    # Summary
    print("\n📋 Performance Summary")
    print("=" * 50)

    avg_fresh_time = sum(r["fresh_time"] for r in results if r["fresh_time"]) / len([r for r in results if r["fresh_time"]])
    avg_cached_time = sum(r["cached_time"] for r in results if r["cached_time"]) / len([r for r in results if r["cached_time"]])

    print(f"Average fresh count time: {avg_fresh_time:.2f}s")
    print(f"Average cached count time: {avg_cached_time:.2f}s")
    print(f"Overall cache speedup: {avg_fresh_time / avg_cached_time:.1f}x")
    
    # Test cache stats
    try:
        response = requests.get(f"{API_BASE_URL}/search/count/cache/stats")
        cache_stats = response.json()
        print(f"\n💾 Cache Stats:")
        print(f"Cache size: {cache_stats.get('cache_size', 0)}")
        print(f"Max cache size: {cache_stats.get('max_cache_size', 0)}")
    except Exception as e:
        print(f"❌ Cache stats failed: {e}")

def test_cache_management():
    """Test cache management endpoints"""
    print("\n🧹 Testing Cache Management")
    print("=" * 30)
    
    # Clear cache
    try:
        response = requests.post(f"{API_BASE_URL}/search/count/cache/clear")
        result = response.json()
        print(f"✅ Cache cleared: {result}")
    except Exception as e:
        print(f"❌ Cache clear failed: {e}")
    
    # Check stats after clear
    try:
        response = requests.get(f"{API_BASE_URL}/search/count/cache/stats")
        stats = response.json()
        print(f"📊 Cache stats after clear: {stats}")
    except Exception as e:
        print(f"❌ Cache stats failed: {e}")

if __name__ == "__main__":
    print("🚀 Starting Count Performance Tests")
    print("Make sure your API server is running at", API_BASE_URL)
    print()
    
    # Test cache management first
    test_cache_management()
    
    # Run performance tests
    asyncio.run(test_count_performance())
    
    print("\n✅ Tests completed!")
    print("\n💡 Recommendations:")
    print("1. Enable caching for repeated queries (significant speedup)")
    print("2. Use include_count=True in search for single-request results")
    print("3. Clear cache periodically if data changes frequently")
    print("4. Monitor cache hit rates for optimization")
